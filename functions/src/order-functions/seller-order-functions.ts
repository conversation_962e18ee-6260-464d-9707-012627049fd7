import * as admin from "firebase-admin";
import { HttpsError, onCall } from "firebase-functions/v2/https";
import {
  requireAuthentication,
  validateOrderCreationParams,
  validatePurchaseParams,
  validateSellerOwnership,
} from "../services/auth-middleware";
import { createOrder } from "../services/order-creation-service";
import { processSellerPurchase } from "../services/purchase-flow-service";
import { UserType } from "../types";

export const createOrderAsSeller = onCall<{
  sellerId: string;
  collectionId: string;
  price: number;
}>(async (request) => {
  const authRequest = requireAuthentication(request);
  const { sellerId, collectionId, price } = request.data;

  validateOrderCreationParams(request.data, UserType.SELLER);
  validateSellerOwnership(authRequest, sellerId);

  try {
    const db = admin.firestore();

    const result = await createOrder(db, {
      userId: sellerId,
      collectionId,
      price,
      owned_gift_id: null,
      userType: UserType.SELLER,
      secondaryMarketPrice: null,
    });

    return result;
  } catch (error) {
    console.error("Error creating order as seller:", error);
    throw new HttpsError(
      "internal",
      (error as any).message ?? "Server error while creating order."
    );
  }
});

export const makePurchaseAsSeller = onCall<{
  sellerId: string;
  orderId: string;
}>(async (request) => {
  const authRequest = requireAuthentication(request);
  const { sellerId, orderId } = request.data;

  validatePurchaseParams(request.data, UserType.SELLER);
  validateSellerOwnership(authRequest, sellerId);

  try {
    const db = admin.firestore();

    const result = await processSellerPurchase(db, sellerId, orderId);

    return result;
  } catch (error) {
    console.error("Error making purchase as seller:", error);
    throw new HttpsError(
      "internal",
      (error as any).message ?? "Server error while making purchase."
    );
  }
});
